#pragma once

// =================================================================================
// 依赖项 (Dependencies)
// =================================================================================
#include <chrono>
#include <iostream>
#include <memory>
#include <mutex>
#include <spdlog/async.h>
#include <spdlog/sinks/base_sink.h>
#include <spdlog/sinks/msvc_sink.h>
#include <spdlog/spdlog.h>
#include <string>
#include <vector>


// =================================================================================
// 类型定义 (Type Definitions)
// =================================================================================

// 定义回调函数的原型。这个类型定义是公开的，因为导出文件和本文件都需要它。
typedef void (*LogCallback)(int level, long long timestamp_ns, const char *message);

// =================================================================================
// 内部实现 (Internal Implementation - Hidden in an anonymous namespace)
// =================================================================================

namespace { // 使用匿名命名空间来封装所有实现细节，使其在此文件外不可见

    // 全局回调函数指针，只在此 hpp 文件被 include 的编译单元内有效。
    // 配合 SetCallback 函数，可以安全地在不同编译单元间工作。
    LogCallback g_internal_log_callback = nullptr;
    bool g_is_logger_initialized = false; // 移到这里，以便全局访问

    // 自定义 Sink 的模板实现
    template<typename Mutex>
    class InternalCallbackSink : public spdlog::sinks::base_sink<Mutex> {
    protected:
        void sink_it_(const spdlog::details::log_msg &msg) override
        {
            if (g_internal_log_callback)
            {
                spdlog::memory_buf_t formatted;
                this->formatter_->format(msg, formatted);

                auto timestamp_ns =
                        std::chrono::duration_cast<std::chrono::nanoseconds>(msg.time.time_since_epoch()).count();

                g_internal_log_callback(static_cast<int>(msg.level), timestamp_ns, fmt::to_string(formatted).c_str());
            }
        }

        void flush_() override {}
    };

    // 为线程安全版本创建类型别名
    using InternalCallbackSink_mt = InternalCallbackSink<std::mutex>;

} // namespace


// =================================================================================
// 公共接口函数 (Public API Functions)
// =================================================================================

// ★★★ 公共接口函数必须声明为 inline，以避免在多处 include 时出现链接错误 ★★★

// 函数: SetLogCallback
// 作用: 设置将要被调用的回调函数。
//       这个函数会被你的 Exports.cpp 里的导出函数调用。
inline void SetLogCallback(LogCallback callback) { g_internal_log_callback = callback; }


// 函数: InitializeLogger
// 作用: 初始化 spdlog 系统，设置好我们的自定义 Sink。
//       这个函数会被你的 Exports.cpp 里的导出函数调用。
inline void InitLogger()
{
    // ★★★ 核心修正 ★★★
    // 使用静态布尔变量确保我们的初始化逻辑只运行一次。
    if (g_is_logger_initialized)
    {
        return;
    }

    try
    {
        // 1. 创建我们唯一的 Sink：回调 Sink。
        auto callback_sink = std::make_shared<InternalCallbackSink_mt>();
        callback_sink->set_pattern("[%n] %v"); // 设置发送给 Python 的格式

        // 2. 创建一个只包含我们回调 Sink 的 Sink 列表。
        std::vector<spdlog::sink_ptr> sinks = {callback_sink};

        // 3. 正常设置异步日志记录器
        spdlog::init_thread_pool(8192, 1);
        auto async_logger = std::make_shared<spdlog::async_logger>(
                "default", sinks.begin(), sinks.end(), spdlog::thread_pool(), spdlog::async_overflow_policy::block);

        // ★★★ 关键步骤 ★★★
        // 4. 强制将我们创建的 logger 设置为默认 logger。
        //    这会覆盖掉 spdlog 可能已经自动创建的任何默认 logger。
        spdlog::set_default_logger(async_logger);
        spdlog::set_level(spdlog::level::trace);

        // 5. 标记我们的初始化已完成。
        g_is_logger_initialized = true;

    } catch (const spdlog::spdlog_ex &ex)
    {
        std::cerr << "Logger initialization failed: " << ex.what() << std::endl;
    }
}

// 函数: GetLogger
// 作用: 在 C++ 内部获取一个具名的 logger 实例。
//       这是你在项目中最常调用的函数。
inline std::shared_ptr<spdlog::logger> GetLogger(const std::string &name)
{
    auto logger = spdlog::get(name);
    if (!logger)
    {
        if (auto default_logger = spdlog::default_logger())
        {
            logger = default_logger->clone(name);
            spdlog::register_logger(logger);
        }
    }
    return logger;
}


// 函数: ShutdownLogger
// 作用: 安全地关闭 spdlog 日志系统。
inline void ShutdownLogger()
{
    if (!g_is_logger_initialized)
    {
        return;
    }
    GetLogger("LibraryInit")->flush();
    spdlog::shutdown();
    g_is_logger_initialized = false;
}
