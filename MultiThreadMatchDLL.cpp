#include "MultiThreadMatchDLL.h"
#include <ShellScalingApi.h> // 用于 SetProcessDpiAwareness
#include <fstream>
#include <iostream>
#include <memory>
#include <string>
#include <windows.h>
#include <winuser.h> // 用于 SetProcessDPIAware
#include "DebugWindow.h"
#include "Logger.hpp"
#include "MatchingManager.h"


// 全局变量
std::unique_ptr<MatchingManager> g_matching_manager;
std::unique_ptr<DebugWindow> g_debug_window;
std::string g_last_error;
bool g_debug_mode = false; // 新增：全局变量存储调试模式状态

// // 日志辅助函数
// void LogDebug(const std::wstring &message)
// {
//     // 发送到调试器
//     OutputDebugStringW((message + L"\n").c_str());

//     try
//     {
//         // 直接使用WriteConsoleW输出到控制台，避免编码问题
//         HANDLE hStdout = GetStdHandle(STD_OUTPUT_HANDLE);
//         if (hStdout != INVALID_HANDLE_VALUE)
//         {
//             DWORD written;
//             WriteConsoleW(hStdout, message.c_str(), (DWORD) message.length(), &written, NULL);
//             WriteConsoleW(hStdout, L"\n", 1, &written, NULL);
//         }

//         // 同时写入日志文件
//         static std::ofstream logFile;
//         if (!logFile.is_open())
//         {
//             logFile.open("multithreadmatch_dll.log", std::ios::out | std::ios::app);
//             if (logFile.is_open())
//             {
//                 // 写入UTF-8 BOM
//                 unsigned char bom[] = {0xEF, 0xBB, 0xBF};
//                 logFile.write(reinterpret_cast<char *>(bom), 3);
//             }
//         }
//         if (logFile.is_open())
//         {
//             // 转换为UTF-8
//             int size_needed = WideCharToMultiByte(CP_UTF8, 0, message.c_str(), -1, NULL, 0, NULL, NULL);
//             if (size_needed > 0)
//             {
//                 std::string utf8_message(size_needed, 0);
//                 WideCharToMultiByte(CP_UTF8, 0, message.c_str(), -1, &utf8_message[0], size_needed, NULL, NULL);
//                 logFile << utf8_message << std::endl;
//                 logFile.flush();
//             }
//         }
//     } catch (...)
//     {
//         // 忽略日志错误
//     }
// }

// // 错误处理辅助函数
// void SetLastError(const std::string &error)
// {
//     g_last_error = error;
//     LogDebug(L"Error: " + std::wstring(error.begin(), error.end()));
// }

BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved)
{
    // DllMain 是一个敏感的函数，最好让它尽可能快地执行完毕。
    // 只处理进程加载和卸载事件。
    switch (ul_reason_for_call)
    {
        case DLL_PROCESS_ATTACH: {
            // 只保留DLL自身绝对必要的初始化代码。
            // 例如，如果你需要UTF-8控制台输出进行调试：
            SetConsoleOutputCP(CP_UTF8);
            SetConsoleCP(CP_UTF8);
            break;
        }

        case DLL_PROCESS_DETACH: {
            // GetLogger("LibraryInit")->info("DLL已卸载，正在清理资源。");
            std::cout << "DLL已卸载，正在清理资源。" << std::endl;
            // 在DLL卸载时执行必要的清理工作
            // CleanupDebugWindow();
            // CleanupMatching();
            break;
        }
    }

    // 始终返回 TRUE 表示成功。
    return TRUE;
}

MULTITHREADMATCH_API int InitializeMatching(const char *macro_name, void *hwnd, int isLog, int debug_mode)
{
    try
    {
        InitLogger();
        GetLogger("LibraryInit")->info("DLL已初始化，日志系统加载完毕，正在初始化匹配管理器。");

        if (!macro_name)
        {
            GetLogger("LibraryInit")->error("宏名称不能为空");
            return -1;
        }

        // 保存调试模式状态
        g_debug_mode = (debug_mode != 0);

        // 如果已经初始化，先清理
        if (g_matching_manager)
        {
            g_matching_manager->stop();
            g_matching_manager.reset();
        }

        if (g_debug_window)
        {
            g_debug_window->stopRendering();
            g_debug_window.reset();
        }

        // 创建MatchingManager
        HWND target_hwnd = static_cast<HWND>(hwnd);
        target_hwnd = nullptr; // 暂时禁用，自定义句柄
        size_t actual_thread_count = 0;

        g_matching_manager =
                std::make_unique<MatchingManager>(std::string(macro_name), actual_thread_count, target_hwnd);

        // 如果启用调试模式，创建调试窗口但不初始化
        if (g_debug_mode)
        {
            g_debug_window = std::make_unique<DebugWindow>();
            // 只设置关联，不进行初始化
            g_matching_manager->setDebugWindow(g_debug_window.get());
        }

        GetLogger("LibraryInit")->info("匹配管理器创建成功");
        return 0;
    } catch (const std::exception &e)
    {
        GetLogger("LibraryInit")->error("初始化失败: " + std::string(e.what()));
        return -2;
    } catch (...)
    {
        GetLogger("LibraryInit")->error("初始化匹配管理器出现未知错误");
        return -3;
    }
}

MULTITHREADMATCH_API int StartMatching()
{
    try
    {
        if (!g_matching_manager)
        {
            GetLogger("LibraryInit")->error("匹配管理器未初始化");
            return -1;
        }

        bool success = g_matching_manager->start();
        if (!success)
        {
            GetLogger("LibraryInit")->error("启动匹配失败");
            return -2;
        }

        // 如果启用了调试模式，在匹配服务成功启动后，再初始化并显示调试窗口
        if (g_debug_mode && g_debug_window)
        {
            if (!g_debug_window->initialize())
            {
                GetLogger("LibraryInit")->error("初始化调试窗口失败");
                // 返回成功，因为核心功能已启动，仅调试窗口失败
                return 0;
            }
            g_debug_window->show();
            GetLogger("LibraryInit")->info("调试窗口显示成功");
        }


        GetLogger("LibraryInit")->info("匹配启动成功");
        return 0;
    } catch (const std::exception &e)
    {
        GetLogger("LibraryInit")->error("启动匹配失败: " + std::string(e.what()));
        return -3;
    } catch (...)
    {
        GetLogger("LibraryInit")->error("启动调试窗口出现未知错误");
        return -4;
    }
}

MULTITHREADMATCH_API int StopMatching()
{
    try
    {
        GetLogger("LibraryInit")->info("正在停止匹配...");
        // 如果调试窗口在运行，停止渲染
        if (g_debug_window)
        {
            GetLogger("LibraryInit")->info("正在停止调试窗口渲染...");
            g_debug_window->stopRendering();
        }

        if (g_matching_manager)
        {
            g_matching_manager->stop();
        }


        GetLogger("LibraryInit")->info("匹配停止成功");
        return 0;
    } catch (const std::exception &e)
    {
        GetLogger("LibraryInit")->error("停止匹配失败: " + std::string(e.what()));
        return -1;
    } catch (...)
    {
        GetLogger("LibraryInit")->error("停止匹配出现未知错误");
        return -2;
    }
}

MULTITHREADMATCH_API int CleanupMatching()
{
    try
    {
        GetLogger("LibraryInit")->info("正在清理匹配管理器...");

        // 1. 停止匹配管理器的运行，确保没有线程再访问调试窗口
        if (g_matching_manager)
        {
            GetLogger("LibraryInit")->info("正在停止匹配管理器...");
            g_matching_manager->stop();
        }

        // 2. 停止调试窗口的渲染线程
        if (g_debug_window)
        {
            GetLogger("LibraryInit")->info("正在停止调试窗口渲染...");
            g_debug_window->stopRendering();
        }

        // 3. 销毁调试窗口实例
        if (g_debug_window)
        {
            GetLogger("LibraryInit")->info("正在销毁调试窗口...");
            g_debug_window.reset();
        }

        // 4. 销毁匹配管理器实例
        if (g_matching_manager)
        {
            GetLogger("LibraryInit")->info("正在销毁匹配管理器...");
            g_matching_manager.reset();
        }

        // 5. 关闭日志系统
        GetLogger("LibraryInit")->info("正在关闭日志系统...");

        GetLogger("LibraryInit")->info("匹配清理成功");
        ShutdownLogger();
        return 0;
    } catch (const std::exception &e)
    {
        GetLogger("LibraryInit")->error("清理匹配管理器失败: " + std::string(e.what()));
        ShutdownLogger();
        return -1;
    } catch (...)
    {
        GetLogger("LibraryInit")->error("清理匹配管理器出现未知错误");
        ShutdownLogger();
        return -2;
    }
}

MULTITHREADMATCH_API int IsMatchingRunning()
{
    try
    {
        if (!g_matching_manager)
        {
            return 0;
        }

        return g_matching_manager->isRunning() ? 1 : 0;
    } catch (...)
    {
        return 0;
    }
}


MULTITHREADMATCH_API uint64_t GetTotalFramesProcessed()
{
    try
    {
        if (!g_matching_manager)
        {
            return 0;
        }

        return g_matching_manager->GetTotalFramesProcessed();
    } catch (...)
    {
        return 0;
    }
}

MULTITHREADMATCH_API uint64_t GetTotalMatchesFound()
{
    try
    {
        if (!g_matching_manager)
        {
            return 0;
        }

        return g_matching_manager->GetTotalMatchesFound();
    } catch (...)
    {
        return 0;
    }
}

MULTITHREADMATCH_API double GetAvgFrameTimeMs()
{
    try
    {
        if (!g_matching_manager)
        {
            return 0.0;
        }

        return g_matching_manager->GetAvgFrameTimeMs();
    } catch (...)
    {
        return 0.0;
    }
}

MULTITHREADMATCH_API double GetAvgTemplateMatchTimeMs()
{
    try
    {
        if (!g_matching_manager)
        {
            return 0.0;
        }

        return g_matching_manager->GetAvgTemplateMatchTimeMs();
    } catch (...)
    {
        return 0.0;
    }
}


MULTITHREADMATCH_API const char *GetLastMatchError() { return g_last_error.c_str(); }

// MULTITHREADMATCH_API int WriteLog(const wchar_t *message)
// {
//     try
//     {
//         if (!message)
//             return 0;

//         GetLogger("LibraryInit")->info(std::wstring(message));
//         return 1;
//     } catch (...)
//     {
//             GetLogger("LibraryInit")->error("写入日志失败");
//         return 0;
//     }
// }

// 新增的调试窗口功能
MULTITHREADMATCH_API int InitializeDebugWindow()
{
    try
    {
        if (!g_debug_window)
        {
            g_debug_window = std::make_unique<DebugWindow>();
        }

        if (!g_debug_window->initialize())
        {
            GetLogger("LibraryInit")->error("初始化调试窗口失败");
            return -1;
        }

        // 如果 MatchingManager 已存在，设置调试窗口
        if (g_matching_manager)
        {
            g_matching_manager->setDebugWindow(g_debug_window.get());
        }
        GetLogger("LibraryInit")->info("调试窗口初始化成功");
        return 0;
    } catch (const std::exception &e)
    {
        GetLogger("LibraryInit")->error("初始化调试窗口失败: " + std::string(e.what()));
        return -1;
    } catch (...)
    {
        GetLogger("LibraryInit")->error("初始化调试窗口出现未知错误");
        return -2;
    }
}

MULTITHREADMATCH_API int ShowDebugWindow()
{
    try
    {
        if (!g_debug_window)
        {
            GetLogger("LibraryInit")->error("调试窗口未初始化");
            return -1;
        }

        g_debug_window->show();
        GetLogger("LibraryInit")->info("调试窗口显示成功");
        return 0;
    } catch (const std::exception &e)
    {
        GetLogger("LibraryInit")->error("显示调试窗口失败: " + std::string(e.what()));
        return -1;
    } catch (...)
    {
        GetLogger("LibraryInit")->error("显示调试窗口出现未知错误");
        return -2;
    }
}

MULTITHREADMATCH_API int HideDebugWindow()
{
    try
    {
        if (!g_debug_window)
        {
            return 0; // 已经隐藏
        }

        g_debug_window->hide();
        GetLogger("LibraryInit")->info("调试窗口隐藏成功");
        return 0;
    } catch (const std::exception &e)
    {
        GetLogger("LibraryInit")->error("隐藏调试窗口失败: " + std::string(e.what()));
        return -1;
    } catch (...)
    {
        GetLogger("LibraryInit")->error("隐藏调试窗口出现未知错误");
        return -2;
    }
}

MULTITHREADMATCH_API int IsDebugWindowVisible()
{
    try
    {
        if (!g_debug_window)
        {
            return 0;
        }

        return g_debug_window->isRunning() ? 1 : 0;
    } catch (...)
    {
        return 0;
    }
}

MULTITHREADMATCH_API int CleanupDebugWindow()
{
    try
    {
        if (g_debug_window)
        {
            g_debug_window->cleanup();
            g_debug_window.reset();
        }

        GetLogger("LibraryInit")->info("调试窗口清理成功");
        return 0;
    } catch (const std::exception &e)
    {
        GetLogger("LibraryInit")->error("清理调试窗口失败: " + std::string(e.what()));
        return -1;
    } catch (...)
    {
        GetLogger("LibraryInit")->error("清理调试窗口出现未知错误");
        return -2;
    }
}


MULTITHREADMATCH_API uint32_t GetIsExist(const char *name)
{
    if (!g_matching_manager)
    {
        return 0;
    }

    // 将 const char* 转换为 std::u8string
    std::u8string u8name = std::u8string(reinterpret_cast<const char8_t *>(name));
    return g_matching_manager->GetIsExist(u8name);
}

MULTITHREADMATCH_API uint32_t GetExistTime(const char *name)
{
    if (!g_matching_manager)
    {
        return 0;
    }
    // 将 const char* 转换为 std::u8string
    std::u8string u8name = std::u8string(reinterpret_cast<const char8_t *>(name));
    return g_matching_manager->GetExistTime(u8name);
}

MULTITHREADMATCH_API int PrintTemplateInfo()
{
    if (!g_matching_manager)
    {
        return 0;
    }
    return g_matching_manager->PrintTemplateInfo();
}


void RegisterPythonLogger(LogCallback callback) { SetLogCallback(callback); }
