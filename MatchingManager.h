﻿#pragma once

#include "BaseStruct.h"
#include "Screenshot/Screenshot.hpp"
#include "Tools/BS_thread_pool.hpp"

//  #include "ScreenshotWGC.hpp"
// #include "ScreenshotDC.hpp"

#include <atomic>
#include <chrono>
#include <cstddef>
#include <cstdint>
#include <memory>
#include <mutex>
#include <opencv2/opencv.hpp>
#include <string>
#include <thread>
#include <vector>
#include "Tools/json.hpp"

// #include "ppocr/ppocr.h"
#include "CudaMatch/CudaMatchingManager.hpp"
#include "ppocr/PaddleOcrRecognizer.h"


// 前向声明
class DebugWindow;
class NccMatcher; // 前向声明NccMatcher

class MatchingManager {
public:
    explicit MatchingManager(const std::string &macro_name, size_t thread_count = 0, HWND target_hwnd = nullptr);
    ~MatchingManager();

    // 启动/停止服务
    bool init();
    bool start();
    void stop();
    bool isRunning() const { return running_; }

    // 获取统计信息
    struct Statistics {
        std::atomic<uint64_t> total_frames_processed = 0;
        std::atomic<uint64_t> total_matches_found = 0;
        double avg_frame_time_ms = 0.0;
        double avg_template_match_time_ms = 0.0;
        std::chrono::system_clock::time_point last_update;
        uint64_t last_frame_count = 0;
        double fps = 0.0;
        Statistics() = default;
    };

    // 添加设置调试窗口的方法
    void setDebugWindow(DebugWindow *debug_window);
    uint64_t GetTotalFramesProcessed() const;
    uint64_t GetTotalMatchesFound() const;
    double GetAvgFrameTimeMs() const;
    double GetAvgTemplateMatchTimeMs() const;
    // 查询结果
    uint32_t GetIsExist(std::u8string &name) const;
    uint32_t GetExistTime(std::u8string &name) const;
    // 禁止拷贝
    MatchingManager(const MatchingManager &) = delete;
    MatchingManager &operator=(const MatchingManager &) = delete;
    int PrintTemplateInfo();

private:
    // NCC匹配器实例
    std::unique_ptr<NccMatcher> ncc_matcher_;
    std::unique_ptr<CudaMatchingManager> cuda_matching_manager_;
    // 配置和核心组件
    std::string image_path_;
    std::string config_name_;
    std::string config_skill_name_;
    std::vector<cv::Rect> base_rois_;
    std::chrono::steady_clock::time_point program_start_time_;
    // std::unique_ptr<ScreenshotWGC> screenshot_wgc_;
    // std::unique_ptr<ScreenshotDXGI> screenshot_dxgi_;
    std::unique_ptr<ScreenshotDC> screenshot_dc_;
    std::unique_ptr<BS::thread_pool<BS::tp::none>> thread_pool_; // 这个线程池现在给多区域匹配用
    size_t thread_pool_count_ = 0;
    std::vector<DWORD> worker_affinities_;
    // 模板计数
    int relative_template_count_ = 0;
    int pixel_count_ = 0;
    int ocr_count_ = 0;
    int absolute_template_count_ = 0;

    float ocr_threshold_ = 0.6f;
    // 是否是DXGI截图
    bool is_DXGI_ = false;
    // 是否是单区域截图
    bool is_single_regions_ = false;
    // FPS
    uint32_t FPS_ = 0;
    // 是否使用CUDA
    bool is_cuda_ = false;
    // 配置文件
    nlohmann::json config_json_;
    // 技能配置文件
    nlohmann::json config_skill_json_;
    // 截图
    std::unique_ptr<Screenshot> screenshot_;
    // OCR
    // std::unique_ptr<libmatch::ppocr> ocr_;
    std::unique_ptr<PaddleOcrRecognizer> ocr_;
    // 模板数据
    std::shared_ptr<std::unordered_map<std::u8string, TemplateInfo>> template_map_ =
            std::make_shared<std::unordered_map<std::u8string, TemplateInfo>>();


    // 运行状态
    std::atomic<bool> running_{false};
    std::atomic<bool> stop_requested_{false};
    std::atomic<bool> cuda_ocr_stop_requested_{false};
    std::thread main_loop_thread_;
    std::thread cuda_ocr_thread_;

    // 统计信息
    mutable std::mutex stats_mutex_;
    Statistics statistics_;

    // 添加调试窗口指针
    DebugWindow *debug_window_ = nullptr;
    // 用于检测高分跳变


    // 初始化方法
    bool loadConfig();
    bool initCuda();
    bool initScreenshot();
    bool initOCR();
    // 主循环
    void mainLoop();
    void getCount();
    // 截图转换
    static cv::Mat convertScreenshotToMat(const ScreenshotResult &screenshot);

    // 匹配处理
    void Match(const cv::Mat &screenshot); // 多区域匹配
    bool TemplateMatches(const cv::Mat &screenshot, const TemplateInfo &template_info, MatchResult &matchResult);
    static void PixelMatches(const cv::Mat &screenshot, const TemplateInfo &template_info, MatchResult &matchResult);
    void OcrMatching(std::vector<unsigned char> pixel_data, int width, int height, TemplateInfo *p_template_info,
                     const std::u8string &name);
    void cuda_OcrMatching();

    // ROI计算
    static cv::Rect calculateROI(const TemplateInfo &template_info, const cv::Size &screen_size);

    void updateMatchResult(std::u8string name, TemplateInfo &template_info, MatchResult &result,
                           const std::string &message = "");
    // bool hasResultChanged(size_t index, const MatchResult &new_result) const;

    // 统计更新
    void updateStatistics(double frame_time_ms, double template_time_ms);
    void calculateFPS(uint32_t time);

    // template<typename F_Type, typename... Variants>
    // ScreenshotResult getScreenshot(F_Type func_ptr, const std::variant<Variants...> &var)
    // {
    //     std::visit([func_ptr](const auto &obj) { std::invoke(func_ptr, obj); }, var);
    // };
};
