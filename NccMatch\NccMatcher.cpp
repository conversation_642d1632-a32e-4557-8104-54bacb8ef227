#include "NccMatch/NccMatcher.hpp"
#include <algorithm>
#include <fstream>
#include <immintrin.h> // For AVX2 intrinsics
#include <iostream>
#include <numeric>
#include <omp.h>
#include <opencv2/imgproc.hpp>
#include <stdexcept>
#include <unordered_map>
#include "NccMatch/fft_processor.hpp"
#include "Logger.hpp"   

cv::Size
NccMatcher::calculate_fft_size(const std::shared_ptr<std::unordered_map<std::u8string, TemplateInfo>> &template_map,
                               const cv::Rect &target_roi)
{
    int max_template_w = 0;
    int max_template_h = 0;
    GetLogger("NccMatcher")->info("计算FFT尺寸，目标ROI: " + std::to_string(target_roi.width) + "x" + std::to_string(target_roi.height));
    for (const auto &entry: *template_map)
    {
        if (entry.second.template_type == TemplateType::Relative_Template && entry.second.picture_info.mat)
        {
            if (entry.second.picture_info.mat->cols > max_template_w)
            {
                max_template_w = entry.second.picture_info.mat->cols;
            }
            if (entry.second.picture_info.mat->rows > max_template_h)
            {
                max_template_h = entry.second.picture_info.mat->rows;
            }
            std::u8string u8_name = entry.first;
            std::string name(u8_name.begin(), u8_name.end());
            GetLogger("NccMatcher")->info("  - 模板 [" + name + "]: " + std::to_string(entry.second.picture_info.mat->cols) + "x" + std::to_string(entry.second.picture_info.mat->rows));
        }
    }
    GetLogger("NccMatcher")->info("最大模板尺寸: " + std::to_string(max_template_w) + "x" + std::to_string(max_template_h));

    int fft_width, fft_height;
    if (max_template_w == 0 || max_template_h == 0)
    {
        fft_width = cv::getOptimalDFTSize(target_roi.width);
        fft_height = cv::getOptimalDFTSize(target_roi.height);
    } else
    {
        fft_width = cv::getOptimalDFTSize(target_roi.width + max_template_w - 1);
        fft_height = cv::getOptimalDFTSize(target_roi.height + max_template_h - 1);
    }
    return cv::Size(fft_width, fft_height);
}

NccMatcher::NccMatcher(std::shared_ptr<std::unordered_map<std::u8string, TemplateInfo>> template_map,
                       const cv::Rect &target_roi, size_t num_threads) :
    template_map_(std::move(template_map)), fft_width_(calculate_fft_size(template_map_, target_roi).width),
    fft_height_(calculate_fft_size(template_map_, target_roi).height)
{
    GetLogger("NccMatcher")->info("NccMatcher 初始化开始...");
    GetLogger("NccMatcher")->info("最终确定的FFT尺寸: " + std::to_string(fft_width_) + "x" + std::to_string(fft_height_));

    std::string wisdom_filename =
            "config/fftw_wisdom_" + std::to_string(fft_width_) + "_" + std::to_string(fft_height_) + ".dat";

    if (fftwf_import_wisdom_from_filename(wisdom_filename.c_str()) != 0)
    {
        GetLogger("NccMatcher")->info("加载特定尺寸的FFTW wisdom文件: " + wisdom_filename);
    } else
    {
        GetLogger("NccMatcher")->info("未找到特定尺寸的FFTW wisdom文件: " + wisdom_filename + "。首次匹配将进行测量以生成wisdom，可能会比较慢...");
    }

    if (template_map_->empty())
    {
        return;
    }

    // 1. 初始化目标图像的FFT处理器和缓冲区
    fftwf_init_threads();
    fftwf_plan_with_nthreads(omp_get_max_threads());

    target_fft_processor_ = std::make_unique<FftProcessor>(fft_height_, fft_width_, false, FFTW_MEASURE);

    fftwf_plan_with_nthreads(1);

    target_image_real_.reset(static_cast<float *>(fftwf_malloc(sizeof(float) * fft_height_ * fft_width_)));
    target_image_fft_.reset(
            static_cast<fftwf_complex *>(fftwf_malloc(sizeof(fftwf_complex) * fft_height_ * (fft_width_ / 2 + 1))));
    integral_image_.resize((target_roi.height + 1) * (target_roi.width + 1));
    integral_image_sq_.resize((target_roi.height + 1) * (target_roi.width + 1));

    // 2. 为每个线程预分配缓冲区
    thread_buffers_.resize(num_threads);
    for (size_t i = 0; i < num_threads; ++i)
    {
        thread_buffers_[i].cross_corr_fft.reset(
                static_cast<fftwf_complex *>(fftwf_malloc(sizeof(fftwf_complex) * fft_height_ * (fft_width_ / 2 + 1))));
        thread_buffers_[i].cross_corr_real.reset(
                static_cast<float *>(fftwf_malloc(sizeof(float) * fft_height_ * fft_width_)));
    }

    // 3. 预计算所有模板数据
    for (auto &entry: *template_map_)
    {
        TemplateInfo &tmpl_info = entry.second;
        if (tmpl_info.template_type != TemplateType::Relative_Template || !tmpl_info.picture_info.mat)
            continue;

        try
        {
            const auto &pic_mat = *tmpl_info.picture_info.mat;

            if (pic_mat.cols > target_roi.width || pic_mat.rows > target_roi.height)
            {
                std::u8string u8_name = entry.first;
                std::string name(u8_name.begin(), u8_name.end());
                GetLogger("NccMatcher")->error("错误: 模板 [" + name + "] (" + std::to_string(pic_mat.cols) + "x" + std::to_string(pic_mat.rows) + ") 大于目标ROI (" + std::to_string(target_roi.width) + "x" + std::to_string(target_roi.height) + ")。已跳过此模板。");
                tmpl_info.ncc_data.reset();
                continue;
            }

            tmpl_info.ncc_data = std::make_unique<NccTemplateData>();
            auto &ncc_data = *tmpl_info.ncc_data;

            ncc_data.width = pic_mat.cols;
            ncc_data.height = pic_mat.rows;
            ncc_data.fft_width = fft_width_;
            ncc_data.fft_height = fft_height_;

            cv::Mat gray;
            if (pic_mat.channels() == 1)
                gray = pic_mat;
            else
                cv::cvtColor(pic_mat, gray, cv::COLOR_BGR2GRAY);

            cv::Mat gray_float;
            gray.convertTo(gray_float, CV_32F);

            double sum = 0.0, sum_sq = 0.0;
            const int pixel_count = ncc_data.width * ncc_data.height;
            const float *p_gray = gray_float.ptr<float>(0);
            for (int i = 0; i < pixel_count; ++i)
            {
                sum += p_gray[i];
                sum_sq += p_gray[i] * p_gray[i];
            }
            ncc_data.mean = sum / pixel_count;
            const double norm_sq = sum_sq - (sum * sum) / pixel_count;
            ncc_data.norm = (norm_sq > 1e-9) ? std::sqrt(norm_sq) : 0.0;

            UniqueFftwfFloatArray padded_real(
                    static_cast<float *>(fftwf_malloc(sizeof(float) * fft_width_ * fft_height_)));
            cv::Mat padded_template;
            cv::copyMakeBorder(gray_float, padded_template, 0, fft_height_ - ncc_data.height, 0,
                               fft_width_ - ncc_data.width, cv::BORDER_CONSTANT, cv::Scalar::all(0));
            memcpy(padded_real.get(), padded_template.data, padded_template.total() * padded_template.elemSize());

            const size_t complex_size = static_cast<size_t>(fft_height_) * (fft_width_ / 2 + 1);
            ncc_data.fft_result.reset(static_cast<fftwf_complex *>(fftwf_malloc(sizeof(fftwf_complex) * complex_size)));

            FftProcessor forward_fft(fft_height_, fft_width_, false, FFTW_MEASURE);
            forward_fft.execute(padded_real.get(), ncc_data.fft_result.get());

            ncc_data.ifft_processor = std::make_unique<FftProcessor>(fft_height_, fft_width_, true, FFTW_MEASURE);

        } catch (const std::exception &e)
        {
            std::u8string u8_name = entry.first;
            std::string name(u8_name.begin(), u8_name.end());
            GetLogger("NccMatcher")->error("为模板 [" + name + "] 初始化NCC数据时出错: " + std::string(e.what()));
            tmpl_info.ncc_data.reset();
        }
    }

    fftwf_export_wisdom_to_filename(wisdom_filename.c_str());
    GetLogger("NccMatcher")->info("已将当前尺寸的FFTW wisdom保存到: " + wisdom_filename);

    GetLogger("NccMatcher")->info("NccMatcher 初始化完成。");
}

NccMatcher::~NccMatcher()
{
    GetLogger("NccMatcher")->info("NccMatcher 销毁。");
    fftwf_cleanup_threads();
}

void NccMatcher::match(const cv::Mat &target_image, std::unique_ptr<BS::thread_pool<BS::tp::none>> &pool_,
                       std::function<void(const std::u8string &, MatchResult &)> update_callback)
{

    if (target_image.empty() || target_image.channels() != 1)
    {
        return;
    }

    // Optimized pre-processing: parallel copy and pad
    const int src_rows = target_image.rows;
    const int src_cols = target_image.cols;
    float *target_real_ptr = target_image_real_.get();

    // Zero out the buffer in parallel
    const size_t total_size = static_cast<size_t>(fft_width_) * fft_height_;
#pragma omp parallel for
    for (int i = 0; i < total_size; ++i)
    {
        target_real_ptr[i] = 0.0f;
    }

// Copy and convert the image ROI in parallel
#pragma omp parallel for
    for (int r = 0; r < src_rows; ++r)
    {
        const uint8_t *src_ptr = target_image.ptr<uint8_t>(r);
        float *dest_ptr = target_real_ptr + static_cast<size_t>(r) * fft_width_;
        for (int c = 0; c < src_cols; ++c)
        {
            dest_ptr[c] = static_cast<float>(src_ptr[c]);
        }
    }

    target_fft_processor_->execute(target_image_real_.get(), target_image_fft_.get());

    this->integral_image_width_ = target_image.cols;
    this->integral_image_height_ = target_image.rows;
    compute_integral_images_avx2(target_image);

    std::vector<std::future<void>> futures;
    futures.reserve(template_map_->size());

    for (auto &entry: *template_map_)
    {
        if (entry.second.template_type != TemplateType::Relative_Template || !entry.second.ncc_data)
            continue;

        futures.push_back(pool_->submit_task([this, &entry, &update_callback] {
            const std::optional<size_t> thread_idx_opt = BS::this_thread::get_index();
            if (!thread_idx_opt)
            {
                return;
            }

            TemplateInfo &tmpl_info = entry.second;
            ThreadBuffer &buffer = thread_buffers_[*thread_idx_opt];

            const size_t complex_size = static_cast<size_t>(fft_height_) * (fft_width_ / 2 + 1);
            conjugate_multiply_avx2(target_image_fft_.get(), tmpl_info.ncc_data->fft_result.get(),
                                    buffer.cross_corr_fft.get(), complex_size);

            tmpl_info.ncc_data->ifft_processor->execute(buffer.cross_corr_fft.get(), buffer.cross_corr_real.get());

            PlainMatchResult plain_result = find_best_match_avx2(this, tmpl_info, buffer.cross_corr_real.get(), 0.8f);

            MatchResult result;
            result.success.store(plain_result.success, std::memory_order_relaxed);
            result.x.store(plain_result.x, std::memory_order_relaxed);
            result.y.store(plain_result.y, std::memory_order_relaxed);
            update_callback(entry.first, result);
        }));
    }

    for (auto &fut: futures)
    {
        fut.get();
    }
}

void NccMatcher::compute_integral_images_avx2(const cv::Mat &src)
{
    CV_Assert(src.type() == CV_8UC1);
    const int rows = src.rows;
    const int cols = src.cols;
    const int stride = cols + 1;

    integral_image_.assign((rows + 1) * (cols + 1), 0.0);
    integral_image_sq_.assign((rows + 1) * (cols + 1), 0.0);

    double *integral = integral_image_.data();
    double *sq_integral = integral_image_sq_.data();

    for (int y = 0; y < rows; ++y)
    {
        const uint8_t *src_ptr = src.ptr<uint8_t>(y);
        double *int_row_ptr = &integral[(y + 1) * stride];
        double *sq_int_row_ptr = &sq_integral[(y + 1) * stride];
        const double *prev_int_row_ptr = &integral[y * stride];
        const double *prev_sq_int_row_ptr = &sq_integral[y * stride];

        double row_sum = 0.0;
        double row_sq_sum = 0.0;
        int x = 0;

        for (; x <= cols - 8; x += 8)
        {
            __m128i pixels_u8 = _mm_loadl_epi64(reinterpret_cast<const __m128i *>(src_ptr + x));
            __m256i pixels_i32 = _mm256_cvtepu8_epi32(pixels_u8);
            __m256 pixels_f32 = _mm256_cvtepi32_ps(pixels_i32);
            __m256 pixels_sq_f32 = _mm256_mul_ps(pixels_f32, pixels_f32);

            alignas(32) float vals_f32[8];
            _mm256_store_ps(vals_f32, pixels_f32);
            alignas(32) float sq_vals_f32[8];
            _mm256_store_ps(sq_vals_f32, pixels_sq_f32);

            for (int i = 0; i < 8; ++i)
            {
                row_sum += vals_f32[i]; // Accumulate as double
                row_sq_sum += sq_vals_f32[i];
                int_row_ptr[x + i + 1] = row_sum + prev_int_row_ptr[x + i + 1];
                sq_int_row_ptr[x + i + 1] = row_sq_sum + prev_sq_int_row_ptr[x + i + 1];
            }
        }
        for (; x < cols; ++x)
        {
            double val = static_cast<double>(src_ptr[x]);
            row_sum += val;
            row_sq_sum += val * val;
            int_row_ptr[x + 1] = row_sum + prev_int_row_ptr[x + 1];
            sq_int_row_ptr[x + 1] = row_sq_sum + prev_sq_int_row_ptr[x + 1];
        }
    }
}

void NccMatcher::conjugate_multiply_avx2(const fftwf_complex *a, const fftwf_complex *b, fftwf_complex *c, size_t n)
{
    const size_t num_complex_per_vector = 4;
    const size_t num_vectors = n / num_complex_per_vector;
    for (size_t i = 0; i < num_vectors; ++i)
    {
        const float *a_ptr = reinterpret_cast<const float *>(a + i * num_complex_per_vector);
        const float *b_ptr = reinterpret_cast<const float *>(b + i * num_complex_per_vector);
        float *c_ptr = reinterpret_cast<float *>(c + i * num_complex_per_vector);
        __m256 val_a = _mm256_loadu_ps(a_ptr);
        __m256 val_b = _mm256_loadu_ps(b_ptr);
        __m256 val_a_real = _mm256_moveldup_ps(val_a);
        __m256 val_a_imag = _mm256_movehdup_ps(val_a);
        __m256 val_b_real = _mm256_moveldup_ps(val_b);
        __m256 val_b_imag = _mm256_movehdup_ps(val_b);
        __m256 res_real = _mm256_fmadd_ps(val_a_real, val_b_real, _mm256_mul_ps(val_a_imag, val_b_imag));
        __m256 res_imag = _mm256_fmsub_ps(val_a_imag, val_b_real, _mm256_mul_ps(val_a_real, val_b_imag));
        __m256 final_res = _mm256_blend_ps(res_real, res_imag, 0b10101010);
        _mm256_storeu_ps(c_ptr, final_res);
    }
    for (size_t i = num_vectors * num_complex_per_vector; i < n; ++i)
    {
        c[i][0] = a[i][0] * b[i][0] + a[i][1] * b[i][1];
        c[i][1] = a[i][1] * b[i][0] - a[i][0] * b[i][1];
    }
}

PlainMatchResult NccMatcher::find_best_match_avx2(const NccMatcher *matcher, const TemplateInfo &tmpl_info,
                                                  const float *corr_map, float score_threshold)
{
    const auto &ncc_data = *tmpl_info.ncc_data;
    const int template_h = ncc_data.height;
    const int template_w = ncc_data.width;
    const double N = static_cast<double>(template_w) * template_h;
    const double template_mean = ncc_data.mean;
    const double template_norm = ncc_data.norm;

    if (template_norm < 1e-9)
    {
        return {};
    }

    const float fft_norm_factor = 1.0f / (static_cast<float>(matcher->fft_height_) * matcher->fft_width_);

    PlainMatchResult result;
    float global_max_score = -2.0f;
    int global_best_x = -1, global_best_y = -1;

    const int search_h = matcher->integral_image_height_ - template_h;
    const int search_w = matcher->integral_image_width_ - template_w;

    std::vector<float> row_scores;
    row_scores.reserve(search_w + 1);

    constexpr int PREFETCH_DISTANCE = 1;

    for (int y = 0; y <= search_h; ++y)
    {
        const int stride = matcher->integral_image_width_ + 1;

        if (y + PREFETCH_DISTANCE <= search_h)
        {
            const int prefetch_y = y + PREFETCH_DISTANCE;
            _mm_prefetch(reinterpret_cast<const char *>(matcher->integral_image_.data() + prefetch_y * stride),
                         _MM_HINT_T0);
            _mm_prefetch(reinterpret_cast<const char *>(matcher->integral_image_.data() +
                                                        (prefetch_y + template_h) * stride),
                         _MM_HINT_T0);
            _mm_prefetch(reinterpret_cast<const char *>(matcher->integral_image_sq_.data() + prefetch_y * stride),
                         _MM_HINT_T0);
            _mm_prefetch(reinterpret_cast<const char *>(matcher->integral_image_sq_.data() +
                                                        (prefetch_y + template_h) * stride),
                         _MM_HINT_T0);
        }

        const double *p_sum = matcher->integral_image_.data();
        const double *p_sq_sum = matcher->integral_image_sq_.data();
        row_scores.clear();

        for (int x = 0; x <= search_w; ++x)
        {
            const double sum_I = p_sum[(y + template_h) * stride + (x + template_w)] + p_sum[y * stride + x] -
                                 p_sum[(y + template_h) * stride + x] - p_sum[y * stride + (x + template_w)];
            const double sum_sq_I = p_sq_sum[(y + template_h) * stride + (x + template_w)] + p_sq_sum[y * stride + x] -
                                    p_sq_sum[(y + template_h) * stride + x] - p_sq_sum[y * stride + (x + template_w)];

            const double image_mean = sum_I / N;
            double image_norm = sum_sq_I - (sum_I * sum_I) / N;

            if (image_norm < 1e-9)
            {
                row_scores.push_back(-2.0f);
                continue;
            }
            image_norm = std::sqrt(image_norm);

            const double cross_corr_val = static_cast<double>(corr_map[y * matcher->fft_width_ + x]) * fft_norm_factor;
            const double numerator = cross_corr_val - N * template_mean * image_mean;
            const double denominator = template_norm * image_norm;
            row_scores.push_back(static_cast<float>(numerator / denominator));
        }

        float row_max_score = -2.0f;
        int row_best_x = -1;
        const int scores_size = static_cast<int>(row_scores.size());
        for (int i = 0; i < scores_size; ++i)
        {
            if (row_scores[i] > row_max_score)
            {
                row_max_score = row_scores[i];
                row_best_x = i;
            }
        }

        if (row_max_score > global_max_score)
        {
            global_max_score = row_max_score;
            global_best_x = row_best_x;
            global_best_y = y;
        }
    }

    if (global_max_score >= score_threshold)
    {
        result.success = true;
        result.x = global_best_x;
        result.y = global_best_y;
        result.score = global_max_score;
    }
    return result;
}
